#!/usr/bin/env python3
"""
Validation script for the champions database.
Checks for data quality and completeness.
"""

import json
from collections import Counter


def validate_champions_data(json_file: str):
    """Validate the champions JSON data for completeness and consistency."""
    try:
        with open(json_file, 'r', encoding='utf-8') as file:
            champions = json.load(file)
    except FileNotFoundError:
        print(f"File '{json_file}' not found. Please run merge_champions.py first.")
        return
    except Exception as e:
        print(f"Error reading JSON file: {e}")
        return
    
    print(f"Champions Database Validation Report")
    print("=" * 50)
    print(f"Total champions: {len(champions)}")
    
    # Check for required fields
    required_fields = ['name', 'gender', 'positions', 'species', 'resource', 'range_type', 'regions', 'release_year']
    missing_fields = []
    
    for champion in champions:
        for field in required_fields:
            if field not in champion:
                missing_fields.append(f"{champion.get('name', 'Unknown')}: missing {field}")
    
    if missing_fields:
        print(f"\nMissing fields found:")
        for missing in missing_fields[:10]:  # Show first 10
            print(f"  - {missing}")
        if len(missing_fields) > 10:
            print(f"  ... and {len(missing_fields) - 10} more")
    else:
        print("\n✓ All champions have required fields")
    
    # Statistics
    print(f"\nData Statistics:")
    print(f"- Genders: {dict(Counter(c['gender'] for c in champions))}")
    print(f"- Range types: {dict(Counter(c['range_type'] for c in champions))}")
    print(f"- Resources: {dict(Counter(c['resource'] for c in champions))}")
    
    # Most common species
    species_count = Counter(c['species'] for c in champions)
    print(f"- Top 5 species: {dict(species_count.most_common(5))}")
    
    # Most common regions
    all_regions = []
    for champion in champions:
        all_regions.extend(champion['regions'])
    region_count = Counter(all_regions)
    print(f"- Top 5 regions: {dict(region_count.most_common(5))}")
    
    # Release year range
    years = [c['release_year'] for c in champions if c['release_year']]
    if years:
        print(f"- Release years: {min(years)} - {max(years)}")
    
    # Check for duplicates
    names = [c['name'] for c in champions]
    duplicates = [name for name, count in Counter(names).items() if count > 1]
    if duplicates:
        print(f"\n⚠ Duplicate champions found: {duplicates}")
    else:
        print(f"\n✓ No duplicate champions found")
    
    # Check for unknown values
    unknown_count = 0
    for champion in champions:
        if (champion['gender'] == 'Unknown' or 
            champion['species'] == 'Unknown' or 
            not champion['regions']):
            unknown_count += 1
    
    if unknown_count > 0:
        print(f"\n⚠ {unknown_count} champions have incomplete information")
    else:
        print(f"\n✓ All champions have complete information")
    
    print(f"\nValidation complete!")


if __name__ == "__main__":
    validate_champions_data("champions.json")

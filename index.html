<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LoL Champion Dungeon - Guess the Champion</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Creepster&display=swap" rel="stylesheet">
</head>
<body>
    <div class="dungeon-container">
        <!-- Background effects -->
        <div class="torch torch-left"></div>
        <div class="torch torch-right"></div>
        <div class="fog-overlay"></div>
        
        <!-- Header -->
        <header class="game-header">
            <h1 class="game-title">
                <span class="title-glow">Champion Dungeon</span>
            </h1>
            <p class="game-subtitle">Guess the League of Legends Champion</p>
        </header>

        <!-- Game Stats -->
        <div class="game-stats">
            <div class="stat-item">
                <span class="stat-label">Attempts:</span>
                <span class="stat-value" id="attempts">0</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Wins:</span>
                <span class="stat-value" id="wins">0</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Win Rate:</span>
                <span class="stat-value" id="winRate">0%</span>
            </div>
        </div>

        <!-- Game Area -->
        <main class="game-area">
            <!-- Champion Input -->
            <div class="input-section">
                <div class="input-container">
                    <input 
                        type="text" 
                        id="championInput" 
                        placeholder="Enter champion name..." 
                        autocomplete="off"
                        class="champion-input"
                    >
                    <button id="submitGuess" class="submit-btn">
                        <span>Guess</span>
                    </button>
                </div>
                <div id="autocomplete" class="autocomplete-dropdown"></div>
            </div>

            <!-- Game Status -->
            <div class="game-status" id="gameStatus">
                <p>Guess the mystery champion! Use the clues to narrow down your search.</p>
            </div>

            <!-- Guesses Table -->
            <div class="guesses-container">
                <div class="table-header">
                    <div class="header-cell">Champion</div>
                    <div class="header-cell">Gender</div>
                    <div class="header-cell">Position</div>
                    <div class="header-cell">Species</div>
                    <div class="header-cell">Resource</div>
                    <div class="header-cell">Range</div>
                    <div class="header-cell">Region</div>
                    <div class="header-cell">Year</div>
                </div>
                <div id="guessesTable" class="guesses-table">
                    <!-- Guesses will be added here dynamically -->
                </div>
            </div>

            <!-- Game Controls -->
            <div class="game-controls">
                <button id="newGameBtn" class="control-btn new-game-btn">
                    <span>New Game</span>
                </button>
                <button id="giveUpBtn" class="control-btn give-up-btn">
                    <span>Give Up</span>
                </button>
                <button id="rulesBtn" class="control-btn rules-btn">
                    <span>Rules</span>
                </button>
            </div>
        </main>

        <!-- Victory Modal -->
        <div id="victoryModal" class="modal">
            <div class="modal-content victory-content">
                <div class="victory-header">
                    <h2>🏆 Victory! 🏆</h2>
                </div>
                <div class="victory-body">
                    <div class="champion-reveal">
                        <img id="victoryChampionIcon" src="" alt="Champion" class="victory-icon">
                        <h3 id="victoryChampionName"></h3>
                    </div>
                    <p id="victoryMessage"></p>
                    <div class="victory-stats">
                        <div class="victory-stat">
                            <span class="stat-number" id="victoryAttempts">0</span>
                            <span class="stat-text">attempts</span>
                        </div>
                    </div>
                </div>
                <div class="modal-actions">
                    <button id="playAgainBtn" class="modal-btn primary">Play Again</button>
                    <button id="closeVictoryBtn" class="modal-btn secondary">Close</button>
                </div>
            </div>
        </div>

        <!-- Rules Modal -->
        <div id="rulesModal" class="modal">
            <div class="modal-content rules-content">
                <div class="modal-header">
                    <h2>How to Play</h2>
                    <button id="closeRulesBtn" class="close-btn">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="rules-section">
                        <h3>🎯 Objective</h3>
                        <p>Guess the mystery League of Legends champion using the clues provided after each guess.</p>
                    </div>
                    <div class="rules-section">
                        <h3>🎨 Color Guide</h3>
                        <div class="color-examples">
                            <div class="color-example correct">
                                <span class="color-box"></span>
                                <span>Green = Correct match</span>
                            </div>
                            <div class="color-example partial">
                                <span class="color-box"></span>
                                <span>Yellow = Partial match (for positions/regions)</span>
                            </div>
                            <div class="color-example incorrect">
                                <span class="color-box"></span>
                                <span>Red = Incorrect</span>
                            </div>
                        </div>
                    </div>
                    <div class="rules-section">
                        <h3>📊 Categories</h3>
                        <ul>
                            <li><strong>Gender:</strong> Male, Female, or Other</li>
                            <li><strong>Position:</strong> Top, Jungle, Mid, Bot, Support roles</li>
                            <li><strong>Species:</strong> Human, Yordle, Vastaya, etc.</li>
                            <li><strong>Resource:</strong> Mana, Energy, etc.</li>
                            <li><strong>Range:</strong> Melee or Ranged</li>
                            <li><strong>Region:</strong> Demacia, Noxus, Ionia, etc.</li>
                            <li><strong>Year:</strong> Champion release year</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>

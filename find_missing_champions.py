#!/usr/bin/env python3
"""
Helper script to find champions that are missing from the CSV file.
This helps identify which champions need additional information added.
"""

import json
import csv
import requests
from typing import Set


def get_api_champions() -> Set[str]:
    """Get all champion names from the Riot API."""
    try:
        # Get latest version
        response = requests.get("https://ddragon.leagueoflegends.com/api/versions.json")
        response.raise_for_status()
        version = response.json()[0]
        
        # Get champion data
        url = f"https://ddragon.leagueoflegends.com/cdn/{version}/data/en_US/champion.json"
        response = requests.get(url)
        response.raise_for_status()
        data = response.json()
        
        return {champion_info['name'] for champion_info in data['data'].values()}
    except Exception as e:
        print(f"Error fetching API data: {e}")
        return set()


def get_csv_champions(csv_file: str) -> Set[str]:
    """Get all champion names from the CSV file."""
    champions = set()
    try:
        with open(csv_file, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                champions.add(row['name'].strip())
    except FileNotFoundError:
        print(f"CSV file '{csv_file}' not found.")
    except Exception as e:
        print(f"Error reading CSV file: {e}")
    
    return champions


def main():
    """Find and display missing champions."""
    print("Finding missing champions...")
    
    api_champions = get_api_champions()
    csv_champions = get_csv_champions("champions_info.csv")
    
    missing_champions = api_champions - csv_champions
    
    if missing_champions:
        print(f"\nFound {len(missing_champions)} champions missing from CSV:")
        print("=" * 50)
        
        # Sort for consistent output
        for champion in sorted(missing_champions):
            print(f"- {champion}")
        
        print("\nTo add these champions to your CSV, you can copy the following template:")
        print("=" * 50)
        for champion in sorted(missing_champions):
            print(f"{champion},Unknown,Unknown,Unknown,Unknown")
        
        print("\nThen research and fill in the correct information for each champion:")
        print("- gender: Male/Female/Other")
        print("- species: Human/Vastaya/Yordle/etc")
        print("- regions: Champion's regions (semicolon separated)")
        print("- release_year: Year the champion was released")
    else:
        print("All champions from the API are present in the CSV file!")
    
    print(f"\nSummary:")
    print(f"- API Champions: {len(api_champions)}")
    print(f"- CSV Champions: {len(csv_champions)}")
    print(f"- Missing: {len(missing_champions)}")


if __name__ == "__main__":
    main()

# League of Legends Champions Database Builder

This project creates a comprehensive database of League of Legends champions by combining data from the Riot Games Data Dragon API with additional information about champion characteristics.

## Files

- `merge_champions.py` - Main script that fetches and processes champion data
- `champions_info.csv` - CSV file containing additional champion information (gender, species, regions, release year)
- `requirements.txt` - Python dependencies
- `champions.json` - Output file containing the complete champion database (generated)

## Setup

1. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Ensure the `champions_info.csv` file is complete and accurate. The CSV should contain:
   - `name` - Champion name (must match exactly with Data Dragon API names)
   - `gender` - Male/Female/Other
   - `species` - Human, Vastaya, Yordle, etc.
   - `regions` - Champion's associated regions (use semicolon separator for multiple)
   - `release_year` - Year the champion was released

## Usage

Run the script to generate the champions database:

```bash
python merge_champions.py
```

The script will:
1. Fetch the latest version from Riot Data Dragon API
2. Download champion data from the API
3. Read additional information from the CSV file
4. Merge and process all data
5. Generate `champions.json` with the complete database

## Output Format

The generated `champions.json` contains an array of champion objects with these fields:

```json
{
  "name": "Champion Name",
  "gender": "Male/Female/Other",
  "positions": ["Top", "Jungle", "Mid", "Bot", "Support"],
  "species": "Human/Vastaya/Yordle/etc",
  "resource": "Mana/Energy/etc",
  "range_type": "Melee/Ranged",
  "regions": ["Region1", "Region2"],
  "release_year": 2023
}
```

## Data Sources

- **Riot Data Dragon API**: Champion names, tags (positions), and resource types
- **CSV File**: Gender, species, regions, and release year information
- **Derived Data**: Range type is determined from champion tags (Mage/Marksman = Ranged, others = Melee)

## Notes

- The script will warn you if any champions are missing additional information
- Champion names in the CSV must match exactly with the Data Dragon API names
- Multiple regions should be separated by semicolons in the CSV
- The script sorts champions alphabetically in the output file

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', serif;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    color: #e8e8e8;
    min-height: 100vh;
    overflow-x: hidden;
}

/* Dungeon Container */
.dungeon-container {
    position: relative;
    min-height: 100vh;
    padding: 20px;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 48, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.05) 0%, transparent 50%);
}

/* Atmospheric Effects */
.torch {
    position: fixed;
    width: 60px;
    height: 120px;
    top: 10%;
    z-index: 1;
}

.torch-left {
    left: 5%;
    background: radial-gradient(ellipse at center, rgba(255, 140, 0, 0.8) 0%, rgba(255, 69, 0, 0.4) 40%, transparent 70%);
    animation: flicker 2s infinite alternate;
}

.torch-right {
    right: 5%;
    background: radial-gradient(ellipse at center, rgba(255, 140, 0, 0.8) 0%, rgba(255, 69, 0, 0.4) 40%, transparent 70%);
    animation: flicker 2.5s infinite alternate;
}

@keyframes flicker {
    0% { opacity: 0.8; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.05); }
    100% { opacity: 0.9; transform: scale(0.98); }
}

.fog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 30% 70%, rgba(100, 100, 150, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(150, 100, 100, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: 1;
    animation: fogMove 20s infinite linear;
}

@keyframes fogMove {
    0% { transform: translateX(-10px) translateY(-5px); }
    50% { transform: translateX(10px) translateY(5px); }
    100% { transform: translateX(-10px) translateY(-5px); }
}

/* Header Styles */
.game-header {
    text-align: center;
    margin-bottom: 30px;
    position: relative;
    z-index: 2;
}

.game-title {
    font-family: 'Creepster', cursive;
    font-size: 3.5rem;
    margin-bottom: 10px;
    text-shadow: 0 0 20px rgba(255, 140, 0, 0.5);
}

.title-glow {
    background: linear-gradient(45deg, #ff8c00, #ffd700, #ff6347);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    0% { filter: brightness(1) drop-shadow(0 0 10px rgba(255, 140, 0, 0.5)); }
    100% { filter: brightness(1.2) drop-shadow(0 0 20px rgba(255, 140, 0, 0.8)); }
}

.game-subtitle {
    font-size: 1.2rem;
    color: #b8860b;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Game Stats */
.game-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-bottom: 30px;
    position: relative;
    z-index: 2;
}

.stat-item {
    background: linear-gradient(135deg, rgba(30, 30, 60, 0.8), rgba(50, 50, 80, 0.6));
    padding: 15px 25px;
    border-radius: 10px;
    border: 2px solid rgba(184, 134, 11, 0.3);
    box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    text-align: center;
    backdrop-filter: blur(5px);
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    color: #b8860b;
    margin-bottom: 5px;
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 600;
    color: #ffd700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Game Area */
.game-area {
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

/* Input Section */
.input-section {
    margin-bottom: 30px;
    position: relative;
}

.input-container {
    display: flex;
    gap: 15px;
    max-width: 600px;
    margin: 0 auto;
}

.champion-input {
    flex: 1;
    padding: 15px 20px;
    font-size: 1.1rem;
    background: linear-gradient(135deg, rgba(20, 20, 40, 0.9), rgba(30, 30, 50, 0.8));
    border: 2px solid rgba(184, 134, 11, 0.4);
    border-radius: 8px;
    color: #e8e8e8;
    font-family: 'Cinzel', serif;
    box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.3),
        inset 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.champion-input:focus {
    outline: none;
    border-color: #ffd700;
    box-shadow: 
        0 4px 20px rgba(0, 0, 0, 0.4),
        0 0 15px rgba(255, 215, 0, 0.3),
        inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.champion-input::placeholder {
    color: rgba(232, 232, 232, 0.5);
}

.submit-btn {
    padding: 15px 30px;
    background: linear-gradient(135deg, #b8860b, #daa520);
    border: none;
    border-radius: 8px;
    color: #1a1a2e;
    font-family: 'Cinzel', serif;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.submit-btn:hover {
    background: linear-gradient(135deg, #daa520, #ffd700);
    transform: translateY(-2px);
    box-shadow: 
        0 6px 20px rgba(0, 0, 0, 0.4),
        0 0 15px rgba(255, 215, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.submit-btn:active {
    transform: translateY(0);
}

/* Autocomplete */
.autocomplete-dropdown {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 465px;
    background: linear-gradient(135deg, rgba(20, 20, 40, 0.95), rgba(30, 30, 50, 0.9));
    border: 2px solid rgba(184, 134, 11, 0.4);
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

.autocomplete-item {
    padding: 12px 20px;
    cursor: pointer;
    border-bottom: 1px solid rgba(184, 134, 11, 0.2);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 12px;
}

.autocomplete-item:hover,
.autocomplete-item.selected {
    background: linear-gradient(135deg, rgba(184, 134, 11, 0.2), rgba(218, 165, 32, 0.1));
    color: #ffd700;
}

.autocomplete-item img {
    width: 30px;
    height: 30px;
    border-radius: 4px;
    border: 1px solid rgba(184, 134, 11, 0.3);
}

/* Game Status */
.game-status {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, rgba(30, 30, 60, 0.6), rgba(50, 50, 80, 0.4));
    border-radius: 10px;
    border: 2px solid rgba(184, 134, 11, 0.3);
    box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
}

.game-status p {
    font-size: 1.1rem;
    color: #e8e8e8;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Guesses Container */
.guesses-container {
    background: linear-gradient(135deg, rgba(20, 20, 40, 0.8), rgba(30, 30, 50, 0.6));
    border-radius: 12px;
    border: 2px solid rgba(184, 134, 11, 0.3);
    overflow: hidden;
    box-shadow: 
        0 8px 25px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    margin-bottom: 30px;
}

.table-header {
    display: grid;
    grid-template-columns: 120px repeat(7, 1fr);
    background: linear-gradient(135deg, rgba(184, 134, 11, 0.3), rgba(218, 165, 32, 0.2));
    border-bottom: 2px solid rgba(184, 134, 11, 0.4);
}

.header-cell {
    padding: 15px 10px;
    font-weight: 600;
    text-align: center;
    color: #ffd700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    border-right: 1px solid rgba(184, 134, 11, 0.2);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.header-cell:last-child {
    border-right: none;
}

.guesses-table {
    min-height: 100px;
}

.guess-row {
    display: grid;
    grid-template-columns: 120px repeat(7, 1fr);
    border-bottom: 1px solid rgba(184, 134, 11, 0.2);
    transition: all 0.3s ease;
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.guess-cell {
    padding: 15px 10px;
    text-align: center;
    border-right: 1px solid rgba(184, 134, 11, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.guess-cell:last-child {
    border-right: none;
}

.guess-cell.champion-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-align: left;
}

.guess-cell.champion-cell img {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    border: 2px solid rgba(184, 134, 11, 0.3);
}

.guess-cell.champion-cell .champion-name {
    font-weight: 600;
    font-size: 0.9rem;
}

/* Feedback Colors */
.guess-cell.correct {
    background: linear-gradient(135deg, rgba(34, 139, 34, 0.8), rgba(50, 205, 50, 0.6));
    color: #ffffff;
    box-shadow: inset 0 0 10px rgba(34, 139, 34, 0.3);
}

.guess-cell.partial {
    background: linear-gradient(135deg, rgba(255, 165, 0, 0.8), rgba(255, 215, 0, 0.6));
    color: #1a1a2e;
    box-shadow: inset 0 0 10px rgba(255, 165, 0, 0.3);
}

.guess-cell.incorrect {
    background: linear-gradient(135deg, rgba(220, 20, 60, 0.8), rgba(255, 69, 0, 0.6));
    color: #ffffff;
    box-shadow: inset 0 0 10px rgba(220, 20, 60, 0.3);
}

/* Game Controls */
.game-controls {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
}

.control-btn {
    padding: 12px 25px;
    border: none;
    border-radius: 8px;
    font-family: 'Cinzel', serif;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.new-game-btn {
    background: linear-gradient(135deg, #228b22, #32cd32);
    color: white;
}

.new-game-btn:hover {
    background: linear-gradient(135deg, #32cd32, #7fff00);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

.give-up-btn {
    background: linear-gradient(135deg, #dc143c, #ff6347);
    color: white;
}

.give-up-btn:hover {
    background: linear-gradient(135deg, #ff6347, #ff4500);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

.rules-btn {
    background: linear-gradient(135deg, #4682b4, #87ceeb);
    color: white;
}

.rules-btn:hover {
    background: linear-gradient(135deg, #87ceeb, #add8e6);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: linear-gradient(135deg, rgba(20, 20, 40, 0.95), rgba(30, 30, 50, 0.9));
    margin: 5% auto;
    padding: 0;
    border: 2px solid rgba(184, 134, 11, 0.5);
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    box-shadow: 
        0 20px 60px rgba(0, 0, 0, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 25px 30px;
    border-bottom: 2px solid rgba(184, 134, 11, 0.3);
    position: relative;
}

.modal-header h2 {
    color: #ffd700;
    font-size: 1.8rem;
    text-align: center;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.close-btn {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    font-size: 2rem;
    color: #b8860b;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-btn:hover {
    color: #ffd700;
}

.modal-body {
    padding: 30px;
}

.modal-actions {
    padding: 20px 30px;
    border-top: 2px solid rgba(184, 134, 11, 0.3);
    display: flex;
    justify-content: center;
    gap: 15px;
}

.modal-btn {
    padding: 12px 25px;
    border: none;
    border-radius: 8px;
    font-family: 'Cinzel', serif;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.modal-btn.primary {
    background: linear-gradient(135deg, #b8860b, #daa520);
    color: #1a1a2e;
}

.modal-btn.primary:hover {
    background: linear-gradient(135deg, #daa520, #ffd700);
    transform: translateY(-2px);
}

.modal-btn.secondary {
    background: linear-gradient(135deg, rgba(100, 100, 120, 0.8), rgba(120, 120, 140, 0.6));
    color: #e8e8e8;
}

.modal-btn.secondary:hover {
    background: linear-gradient(135deg, rgba(120, 120, 140, 0.9), rgba(140, 140, 160, 0.7));
    transform: translateY(-2px);
}

/* Victory Modal Specific */
.victory-content {
    text-align: center;
}

.victory-header h2 {
    font-size: 2.5rem;
    background: linear-gradient(45deg, #ffd700, #ffed4e, #ffd700);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: victoryGlow 2s ease-in-out infinite alternate;
}

@keyframes victoryGlow {
    0% { filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.5)); }
    100% { filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8)); }
}

.champion-reveal {
    margin: 20px 0;
}

.victory-icon {
    width: 100px;
    height: 100px;
    border-radius: 12px;
    border: 3px solid #ffd700;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
    margin-bottom: 15px;
}

.champion-reveal h3 {
    font-size: 1.8rem;
    color: #ffd700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.victory-stats {
    margin: 20px 0;
}

.victory-stat {
    display: inline-block;
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: #ffd700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.stat-text {
    display: block;
    font-size: 1rem;
    color: #b8860b;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Rules Modal Specific */
.rules-section {
    margin-bottom: 25px;
}

.rules-section h3 {
    color: #ffd700;
    font-size: 1.3rem;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.rules-section p,
.rules-section ul {
    color: #e8e8e8;
    line-height: 1.6;
}

.rules-section ul {
    padding-left: 20px;
}

.rules-section li {
    margin-bottom: 8px;
}

.color-examples {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 15px;
}

.color-example {
    display: flex;
    align-items: center;
    gap: 15px;
}

.color-box {
    width: 30px;
    height: 30px;
    border-radius: 6px;
    border: 2px solid rgba(184, 134, 11, 0.3);
}

.color-example.correct .color-box {
    background: linear-gradient(135deg, rgba(34, 139, 34, 0.8), rgba(50, 205, 50, 0.6));
}

.color-example.partial .color-box {
    background: linear-gradient(135deg, rgba(255, 165, 0, 0.8), rgba(255, 215, 0, 0.6));
}

.color-example.incorrect .color-box {
    background: linear-gradient(135deg, rgba(220, 20, 60, 0.8), rgba(255, 69, 0, 0.6));
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-title {
        font-size: 2.5rem;
    }
    
    .game-stats {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }
    
    .input-container {
        flex-direction: column;
        gap: 10px;
    }
    
    .table-header,
    .guess-row {
        grid-template-columns: 100px repeat(7, 1fr);
        font-size: 0.8rem;
    }
    
    .header-cell,
    .guess-cell {
        padding: 10px 5px;
    }
    
    .guess-cell.champion-cell img {
        width: 30px;
        height: 30px;
    }
    
    .game-controls {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }
    
    .control-btn {
        width: 200px;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
}

@media (max-width: 480px) {
    .table-header,
    .guess-row {
        grid-template-columns: 80px repeat(7, 1fr);
        font-size: 0.7rem;
    }
    
    .header-cell,
    .guess-cell {
        padding: 8px 3px;
    }
    
    .guess-cell.champion-cell {
        flex-direction: column;
        gap: 4px;
    }
    
    .guess-cell.champion-cell img {
        width: 25px;
        height: 25px;
    }
    
    .champion-name {
        font-size: 0.7rem !important;
    }
}

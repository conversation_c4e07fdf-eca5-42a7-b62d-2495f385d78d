'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';

interface Champion {
  name: string;
  gender: string;
  positions: string[];
  species: string;
  resource: string;
  range_type: string;
  regions: string[];
  release_year: number;
  icon: string;
  key: string;
}

interface GameStats {
  totalGames: number;
  wins: number;
}

export default function ChampionDungeon() {
  const [champions, setChampions] = useState<Champion[]>([]);
  const [currentChampion, setCurrentChampion] = useState<Champion | null>(null);
  const [guesses, setGuesses] = useState<Champion[]>([]);
  const [gameOver, setGameOver] = useState(false);
  const [gameStatus, setGameStatus] = useState('A new champion awaits! Start guessing...');
  const [championInput, setChampionInput] = useState('');
  const [autocompleteMatches, setAutocompleteMatches] = useState<Champion[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [showAutocomplete, setShowAutocomplete] = useState(false);
  const [showVictoryModal, setShowVictoryModal] = useState(false);
  const [showRulesModal, setShowRulesModal] = useState(false);
  const [gameStats, setGameStats] = useState<GameStats>({ totalGames: 0, wins: 0 });

  const inputRef = useRef<HTMLInputElement>(null);

  // Load champions data
  useEffect(() => {
    const loadChampions = async () => {
      try {
        const response = await fetch('/champions.json');
        const data = await response.json();
        setChampions(data);
        console.log(`Loaded ${data.length} champions`);
      } catch (error) {
        console.error('Error loading champions:', error);
        setGameStatus('Error loading champion data. Please refresh the page.');
      }
    };

    loadChampions();
  }, []);

  // Load game stats from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('lolChampionDungeonStats');
    if (saved) {
      setGameStats(JSON.parse(saved));
    }
  }, []);

  // Start new game when champions are loaded
  useEffect(() => {
    if (champions.length > 0 && !currentChampion) {
      startNewGame();
    }
  }, [champions]);

  // Handle input changes for autocomplete
  useEffect(() => {
    const query = championInput.toLowerCase().trim();
    
    if (query.length === 0) {
      setShowAutocomplete(false);
      return;
    }
    
    const matches = champions.filter(champion => 
      champion.name.toLowerCase().includes(query) &&
      !guesses.some(guess => guess.name === champion.name)
    ).slice(0, 8);
    
    setAutocompleteMatches(matches);
    setShowAutocomplete(matches.length > 0);
    setSelectedIndex(-1);
  }, [championInput, champions, guesses]);

  const startNewGame = () => {
    if (champions.length === 0) return;
    
    const randomChampion = champions[Math.floor(Math.random() * champions.length)];
    setCurrentChampion(randomChampion);
    setGuesses([]);
    setGameOver(false);
    setGameStatus('A new champion awaits! Start guessing...');
    setChampionInput('');
    setShowAutocomplete(false);
    setShowVictoryModal(false);
    
    console.log('New game started. Target champion:', randomChampion.name);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex(prev => 
        prev < autocompleteMatches.length - 1 ? prev + 1 : 0
      );
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex(prev => 
        prev > 0 ? prev - 1 : autocompleteMatches.length - 1
      );
    } else if (e.key === 'Enter') {
      e.preventDefault();
      if (selectedIndex >= 0 && selectedIndex < autocompleteMatches.length) {
        selectChampion(autocompleteMatches[selectedIndex].name);
      } else {
        submitGuess();
      }
    } else if (e.key === 'Escape') {
      setShowAutocomplete(false);
      setSelectedIndex(-1);
    }
  };

  const selectChampion = (championName: string) => {
    setChampionInput(championName);
    setShowAutocomplete(false);
    setSelectedIndex(-1);
    setTimeout(() => submitGuess(), 0);
  };

  const submitGuess = () => {
    if (gameOver) return;
    
    const guessName = championInput.trim();
    if (!guessName) return;
    
    const champion = champions.find(c => c.name.toLowerCase() === guessName.toLowerCase());
    if (!champion) {
      setGameStatus('Champion not found. Please select from the dropdown.');
      return;
    }
    
    if (guesses.some(g => g.name === champion.name)) {
      setGameStatus('You already guessed this champion!');
      return;
    }
    
    // Add guess
    const newGuesses = [...guesses, champion];
    setGuesses(newGuesses);
    setChampionInput('');
    setShowAutocomplete(false);
    
    // Check if correct
    if (champion.name === currentChampion?.name) {
      winGame();
    } else {
      setGameStatus(`Not ${champion.name}. Keep trying!`);
    }
  };

  const winGame = () => {
    setGameOver(true);
    const newStats = {
      totalGames: gameStats.totalGames + 1,
      wins: gameStats.wins + 1
    };
    setGameStats(newStats);
    localStorage.setItem('lolChampionDungeonStats', JSON.stringify(newStats));
    
    setGameStatus(`🎉 Congratulations! You found ${currentChampion?.name}!`);
    setTimeout(() => setShowVictoryModal(true), 1000);
  };

  const giveUp = () => {
    if (gameOver || !currentChampion) return;
    
    setGameOver(true);
    const newStats = {
      totalGames: gameStats.totalGames + 1,
      wins: gameStats.wins
    };
    setGameStats(newStats);
    localStorage.setItem('lolChampionDungeonStats', JSON.stringify(newStats));
    
    setGameStatus(`The champion was ${currentChampion.name}. Better luck next time!`);
    
    // Add the correct answer to the table
    setGuesses(prev => [...prev, currentChampion]);
  };

  const getComparisonClass = (guessValue: any, targetValue: any, type: string): string => {
    if (type === 'exact') {
      return guessValue === targetValue ? 'correct' : 'incorrect';
    } else if (type === 'array') {
      if (Array.isArray(guessValue) && Array.isArray(targetValue)) {
        const hasMatch = guessValue.some(item => targetValue.includes(item));
        const isExactMatch = guessValue.length === targetValue.length && 
                            guessValue.every(item => targetValue.includes(item));
        return isExactMatch ? 'correct' : (hasMatch ? 'partial' : 'incorrect');
      }
      return guessValue === targetValue ? 'correct' : 'incorrect';
    } else if (type === 'year') {
      if (guessValue === targetValue) {
        return 'correct';
      } else {
        return 'incorrect';
      }
    }
    return 'incorrect';
  };

  const getYearDisplay = (guessYear: number, targetYear: number): string => {
    if (guessYear === targetYear) {
      return guessYear.toString();
    } else if (guessYear < targetYear) {
      return `${guessYear} ↑`;
    } else {
      return `${guessYear} ↓`;
    }
  };

  const getYearClass = (guessYear: number, targetYear: number): string => {
    if (guessYear === targetYear) {
      return 'correct';
    } else {
      return 'partial'; // Always yellow for directional hints
    }
  };

  return (
    <div className="min-h-screen parchment-texture text-gray-800 relative overflow-hidden">
      {/* Background Decorative Elements */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-20 left-20 w-32 h-32 leather-texture rounded-full shadow-2xl transform rotate-12"></div>
        <div className="absolute top-40 right-32 w-24 h-24 metal-texture rounded-lg shadow-xl transform -rotate-6"></div>
        <div className="absolute bottom-32 left-40 w-28 h-28 stone-texture rounded-full shadow-2xl transform rotate-45"></div>
        <div className="absolute bottom-20 right-20 w-36 h-20 leather-texture rounded-lg shadow-xl transform -rotate-12"></div>
      </div>

      <div className="relative z-10 p-4 md:p-8">
        {/* Header */}
        <header className="text-center mb-12">
          <h1 className="skeu-title text-5xl md:text-7xl font-bold mb-6 drop-shadow-lg">
            Champion Dungeon
          </h1>
          <div className="skeu-card inline-block px-8 py-4 mx-auto">
            <p className="text-xl md:text-2xl font-semibold text-amber-800">
              Guess the League of Legends Champion
            </p>
          </div>
        </header>

        {/* Game Area */}
        <main className="max-w-7xl mx-auto">
          {/* Input Section */}
          <div className="mb-12 relative">
            <div className="skeu-card p-6 max-w-3xl mx-auto">
              <div className="flex gap-6">
                <div className="flex-1 relative">
                  <input
                    ref={inputRef}
                    type="text"
                    value={championInput}
                    onChange={(e) => setChampionInput(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder="Enter champion name..."
                    className="skeu-input w-full p-4 text-lg font-semibold text-amber-900 placeholder-amber-600 focus:outline-none"
                  />

                  {/* Autocomplete Dropdown */}
                  {showAutocomplete && (
                    <div className="skeu-dropdown absolute top-full left-0 right-0 max-h-64 overflow-y-auto z-50">
                      {autocompleteMatches.map((champion, index) => (
                        <div
                          key={champion.name}
                          className={`skeu-dropdown-item p-4 cursor-pointer flex items-center gap-4 ${
                            index === selectedIndex ? 'selected' : ''
                          }`}
                          onClick={() => selectChampion(champion.name)}
                        >
                          <div className="relative">
                            <Image
                              src={champion.icon}
                              alt={champion.name}
                              width={36}
                              height={36}
                              className="rounded-lg border-2 border-amber-600 shadow-md"
                            />
                          </div>
                          <span className="font-semibold text-amber-900">{champion.name}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
                <button
                  onClick={submitGuess}
                  className="skeu-button px-8 py-4 text-amber-900 font-bold text-lg"
                >
                  Guess
                </button>
              </div>
            </div>
          </div>

          {/* Game Status */}
          <div className="text-center mb-10">
            <div className="skeu-card p-6 max-w-2xl mx-auto">
              <p className="text-xl font-semibold text-amber-800 leading-relaxed">
                {gameStatus}
              </p>
            </div>
          </div>

          {/* Guesses Table */}
          <div className="skeu-table overflow-hidden mb-12">
            {/* Table Header */}
            <div className="skeu-table-header grid grid-cols-8 text-amber-100">
              <div className="p-4 font-bold text-center text-sm md:text-base">Champion</div>
              <div className="p-4 font-bold text-center text-sm md:text-base">Gender</div>
              <div className="p-4 font-bold text-center text-sm md:text-base">Position</div>
              <div className="p-4 font-bold text-center text-sm md:text-base">Species</div>
              <div className="p-4 font-bold text-center text-sm md:text-base">Resource</div>
              <div className="p-4 font-bold text-center text-sm md:text-base">Range</div>
              <div className="p-4 font-bold text-center text-sm md:text-base">Region</div>
              <div className="p-4 font-bold text-center text-sm md:text-base">Year</div>
            </div>

            {/* Guesses */}
            <div className="min-h-[120px] p-2">
              {guesses.slice().reverse().map((guess, index) => (
                <div key={`${guess.name}-${index}`} className="grid grid-cols-8 border-b border-amber-200 animate-fadeIn hover:bg-amber-50/30 transition-colors">
                  {/* Champion */}
                  <div className="p-4 flex items-center justify-center gap-3">
                    <div className="relative">
                      <Image
                        src={guess.icon}
                        alt={guess.name}
                        width={40}
                        height={40}
                        className="rounded-lg border-2 border-amber-600 shadow-md"
                      />
                      <div className="absolute -inset-1 bg-gradient-to-r from-amber-400 to-amber-600 rounded-lg opacity-20 blur-sm"></div>
                    </div>
                    <span className="text-sm font-bold text-amber-900 hidden md:block">{guess.name}</span>
                  </div>

                  {/* Gender */}
                  <div className={`p-4 text-center text-sm font-semibold rounded-md m-1 ${getComparisonClass(guess.gender, currentChampion?.gender, 'exact')} cell-feedback`}>
                    {guess.gender}
                  </div>

                  {/* Position */}
                  <div className={`p-4 text-center text-xs md:text-sm font-semibold rounded-md m-1 ${getComparisonClass(guess.positions, currentChampion?.positions, 'array')} cell-feedback`}>
                    {guess.positions.join(', ')}
                  </div>

                  {/* Species */}
                  <div className={`p-4 text-center text-sm font-semibold rounded-md m-1 ${getComparisonClass(guess.species, currentChampion?.species, 'exact')} cell-feedback`}>
                    {guess.species}
                  </div>

                  {/* Resource */}
                  <div className={`p-4 text-center text-sm font-semibold rounded-md m-1 ${getComparisonClass(guess.resource, currentChampion?.resource, 'exact')} cell-feedback`}>
                    {guess.resource}
                  </div>

                  {/* Range */}
                  <div className={`p-4 text-center text-sm font-semibold rounded-md m-1 ${getComparisonClass(guess.range_type, currentChampion?.range_type, 'exact')} cell-feedback`}>
                    {guess.range_type}
                  </div>

                  {/* Region */}
                  <div className={`p-4 text-center text-xs md:text-sm font-semibold rounded-md m-1 ${getComparisonClass(guess.regions, currentChampion?.regions, 'array')} cell-feedback`}>
                    {guess.regions.join(', ')}
                  </div>

                  {/* Year with directional hints */}
                  <div className={`p-4 text-center text-sm font-bold rounded-md m-1 ${getYearClass(guess.release_year, currentChampion?.release_year || 0)} cell-feedback`}>
                    {getYearDisplay(guess.release_year, currentChampion?.release_year || 0)}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Game Controls */}
          <div className="flex flex-wrap justify-center gap-6 mb-12">
            <button
              onClick={startNewGame}
              className="skeu-button px-8 py-4 text-amber-900 font-bold text-lg bg-gradient-to-r from-emerald-400 to-emerald-500 hover:from-emerald-300 hover:to-emerald-400"
              style={{
                background: 'linear-gradient(145deg, #34d399, #10b981)',
                color: '#064e3b'
              }}
            >
              New Game
            </button>
            <button
              onClick={giveUp}
              disabled={gameOver}
              className="skeu-button px-8 py-4 text-white font-bold text-lg disabled:opacity-50 disabled:cursor-not-allowed"
              style={{
                background: gameOver ? 'linear-gradient(145deg, #9ca3af, #6b7280)' : 'linear-gradient(145deg, #ef4444, #dc2626)',
                color: gameOver ? '#374151' : 'white'
              }}
            >
              Give Up
            </button>
            <button
              onClick={() => setShowRulesModal(true)}
              className="skeu-button px-8 py-4 text-white font-bold text-lg"
              style={{
                background: 'linear-gradient(145deg, #3b82f6, #2563eb)',
                color: 'white'
              }}
            >
              Rules
            </button>
          </div>
        </main>
      </div>

      {/* Victory Modal */}
      {showVictoryModal && currentChampion && (
        <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-md">
          <div className="skeu-modal p-10 max-w-lg w-full mx-4 text-center relative">
            {/* Decorative corners */}
            <div className="absolute top-2 left-2 w-6 h-6 metal-texture rounded-full"></div>
            <div className="absolute top-2 right-2 w-6 h-6 metal-texture rounded-full"></div>
            <div className="absolute bottom-2 left-2 w-6 h-6 metal-texture rounded-full"></div>
            <div className="absolute bottom-2 right-2 w-6 h-6 metal-texture rounded-full"></div>

            <h2 className="skeu-title text-4xl font-bold mb-6">🏆 Victory! 🏆</h2>
            <div className="mb-8">
              <div className="relative inline-block mb-6">
                <Image
                  src={currentChampion.icon}
                  alt={currentChampion.name}
                  width={120}
                  height={120}
                  className="mx-auto rounded-xl border-4 border-amber-500 shadow-2xl"
                />
                <div className="absolute -inset-2 bg-gradient-to-r from-amber-400 via-yellow-400 to-amber-400 rounded-xl opacity-30 blur-lg animate-pulse"></div>
              </div>
              <h3 className="text-3xl font-bold text-amber-800 mb-2">{currentChampion.name}</h3>
              <div className="skeu-card inline-block px-6 py-3">
                <p className="text-lg font-semibold text-amber-700">
                  Guessed in {guesses.length} attempt{guesses.length === 1 ? '' : 's'}!
                </p>
              </div>
            </div>
            <div className="flex gap-6 justify-center">
              <button
                onClick={() => {
                  setShowVictoryModal(false);
                  startNewGame();
                }}
                className="skeu-button px-8 py-4 text-amber-900 font-bold text-lg"
              >
                Play Again
              </button>
              <button
                onClick={() => setShowVictoryModal(false)}
                className="skeu-button px-8 py-4 font-bold text-lg"
                style={{
                  background: 'linear-gradient(145deg, #9ca3af, #6b7280)',
                  color: '#374151'
                }}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Rules Modal */}
      {showRulesModal && (
        <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-md">
          <div className="skeu-modal p-8 max-w-3xl w-full mx-4 max-h-[90vh] overflow-y-auto relative">
            {/* Decorative elements */}
            <div className="absolute top-4 left-4 w-8 h-8 leather-texture rounded-full shadow-lg"></div>
            <div className="absolute top-4 right-4 w-8 h-8 stone-texture rounded-full shadow-lg"></div>

            <div className="flex justify-between items-center mb-8">
              <h2 className="skeu-title text-4xl font-bold">How to Play</h2>
              <button
                onClick={() => setShowRulesModal(false)}
                className="skeu-button w-12 h-12 text-2xl font-bold text-amber-900 flex items-center justify-center"
              >
                ×
              </button>
            </div>

            <div className="space-y-8">
              <div className="skeu-card p-6">
                <h3 className="text-2xl font-bold text-amber-800 mb-4 flex items-center gap-3">
                  🎯 <span>Objective</span>
                </h3>
                <p className="text-amber-700 text-lg leading-relaxed">
                  Guess the mystery League of Legends champion using the clues provided after each guess.
                </p>
              </div>

              <div className="skeu-card p-6">
                <h3 className="text-2xl font-bold text-amber-800 mb-4 flex items-center gap-3">
                  🎨 <span>Color Guide</span>
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <div className="w-8 h-8 cell-feedback correct rounded-lg shadow-md"></div>
                    <span className="text-amber-700 font-semibold">Green = Correct match</span>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="w-8 h-8 cell-feedback partial rounded-lg shadow-md"></div>
                    <span className="text-amber-700 font-semibold">Yellow = Partial match (positions/regions) or directional hint (year)</span>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="w-8 h-8 cell-feedback incorrect rounded-lg shadow-md"></div>
                    <span className="text-amber-700 font-semibold">Red = Incorrect</span>
                  </div>
                </div>
              </div>

              <div className="skeu-card p-6">
                <h3 className="text-2xl font-bold text-amber-800 mb-4 flex items-center gap-3">
                  📊 <span>Categories</span>
                </h3>
                <div className="grid md:grid-cols-2 gap-4">
                  <ul className="space-y-3 text-amber-700">
                    <li className="flex items-start gap-2">
                      <span className="font-bold text-amber-800">Gender:</span>
                      <span>Male, Female, or Other</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="font-bold text-amber-800">Position:</span>
                      <span>Top, Jungle, Mid, Bot, Support</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="font-bold text-amber-800">Species:</span>
                      <span>Human, Yordle, Vastaya, etc.</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="font-bold text-amber-800">Resource:</span>
                      <span>Mana, Energy, etc.</span>
                    </li>
                  </ul>
                  <ul className="space-y-3 text-amber-700">
                    <li className="flex items-start gap-2">
                      <span className="font-bold text-amber-800">Range:</span>
                      <span>Melee or Ranged</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="font-bold text-amber-800">Region:</span>
                      <span>Demacia, Noxus, Ionia, etc.</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="font-bold text-amber-800">Year:</span>
                      <span>Release year with ↑ (too low) or ↓ (too high) hints</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';

interface Champion {
  name: string;
  gender: string;
  positions: string[];
  species: string;
  resource: string;
  range_type: string;
  regions: string[];
  release_year: number;
  icon: string;
  key: string;
}

interface GameStats {
  totalGames: number;
  wins: number;
}

export default function ChampionDungeon() {
  const [champions, setChampions] = useState<Champion[]>([]);
  const [currentChampion, setCurrentChampion] = useState<Champion | null>(null);
  const [guesses, setGuesses] = useState<Champion[]>([]);
  const [gameOver, setGameOver] = useState(false);
  const [gameStatus, setGameStatus] = useState('A new champion awaits! Start guessing...');
  const [championInput, setChampionInput] = useState('');
  const [autocompleteMatches, setAutocompleteMatches] = useState<Champion[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [showAutocomplete, setShowAutocomplete] = useState(false);
  const [showVictoryModal, setShowVictoryModal] = useState(false);
  const [showRulesModal, setShowRulesModal] = useState(false);
  const [gameStats, setGameStats] = useState<GameStats>({ totalGames: 0, wins: 0 });

  const inputRef = useRef<HTMLInputElement>(null);

  // Load champions data
  useEffect(() => {
    const loadChampions = async () => {
      try {
        const response = await fetch('/champions.json');
        const data = await response.json();
        setChampions(data);
        console.log(`Loaded ${data.length} champions`);
      } catch (error) {
        console.error('Error loading champions:', error);
        setGameStatus('Error loading champion data. Please refresh the page.');
      }
    };

    loadChampions();
  }, []);

  // Load game stats from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('lolChampionDungeonStats');
    if (saved) {
      setGameStats(JSON.parse(saved));
    }
  }, []);

  // Start new game when champions are loaded
  useEffect(() => {
    if (champions.length > 0 && !currentChampion) {
      startNewGame();
    }
  }, [champions]);

  // Handle input changes for autocomplete
  useEffect(() => {
    const query = championInput.toLowerCase().trim();
    
    if (query.length === 0) {
      setShowAutocomplete(false);
      return;
    }
    
    const matches = champions.filter(champion => 
      champion.name.toLowerCase().includes(query) &&
      !guesses.some(guess => guess.name === champion.name)
    ).slice(0, 8);
    
    setAutocompleteMatches(matches);
    setShowAutocomplete(matches.length > 0);
    setSelectedIndex(-1);
  }, [championInput, champions, guesses]);

  const startNewGame = () => {
    if (champions.length === 0) return;
    
    const randomChampion = champions[Math.floor(Math.random() * champions.length)];
    setCurrentChampion(randomChampion);
    setGuesses([]);
    setGameOver(false);
    setGameStatus('A new champion awaits! Start guessing...');
    setChampionInput('');
    setShowAutocomplete(false);
    setShowVictoryModal(false);
    
    console.log('New game started. Target champion:', randomChampion.name);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex(prev => 
        prev < autocompleteMatches.length - 1 ? prev + 1 : 0
      );
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex(prev => 
        prev > 0 ? prev - 1 : autocompleteMatches.length - 1
      );
    } else if (e.key === 'Enter') {
      e.preventDefault();
      if (selectedIndex >= 0 && selectedIndex < autocompleteMatches.length) {
        selectChampion(autocompleteMatches[selectedIndex].name);
      } else {
        submitGuess();
      }
    } else if (e.key === 'Escape') {
      setShowAutocomplete(false);
      setSelectedIndex(-1);
    }
  };

  const selectChampion = (championName: string) => {
    setChampionInput(championName);
    setShowAutocomplete(false);
    setSelectedIndex(-1);
    setTimeout(() => submitGuess(), 0);
  };

  const submitGuess = () => {
    if (gameOver) return;
    
    const guessName = championInput.trim();
    if (!guessName) return;
    
    const champion = champions.find(c => c.name.toLowerCase() === guessName.toLowerCase());
    if (!champion) {
      setGameStatus('Champion not found. Please select from the dropdown.');
      return;
    }
    
    if (guesses.some(g => g.name === champion.name)) {
      setGameStatus('You already guessed this champion!');
      return;
    }
    
    // Add guess
    const newGuesses = [...guesses, champion];
    setGuesses(newGuesses);
    setChampionInput('');
    setShowAutocomplete(false);
    
    // Check if correct
    if (champion.name === currentChampion?.name) {
      winGame();
    } else {
      setGameStatus(`Not ${champion.name}. Keep trying!`);
    }
  };

  const winGame = () => {
    setGameOver(true);
    const newStats = {
      totalGames: gameStats.totalGames + 1,
      wins: gameStats.wins + 1
    };
    setGameStats(newStats);
    localStorage.setItem('lolChampionDungeonStats', JSON.stringify(newStats));
    
    setGameStatus(`🎉 Congratulations! You found ${currentChampion?.name}!`);
    setTimeout(() => setShowVictoryModal(true), 1000);
  };

  const giveUp = () => {
    if (gameOver || !currentChampion) return;
    
    setGameOver(true);
    const newStats = {
      totalGames: gameStats.totalGames + 1,
      wins: gameStats.wins
    };
    setGameStats(newStats);
    localStorage.setItem('lolChampionDungeonStats', JSON.stringify(newStats));
    
    setGameStatus(`The champion was ${currentChampion.name}. Better luck next time!`);
    
    // Add the correct answer to the table
    setGuesses(prev => [...prev, currentChampion]);
  };

  const getComparisonClass = (guessValue: any, targetValue: any, type: string): string => {
    if (type === 'exact') {
      return guessValue === targetValue ? 'correct' : 'incorrect';
    } else if (type === 'array') {
      if (Array.isArray(guessValue) && Array.isArray(targetValue)) {
        const hasMatch = guessValue.some(item => targetValue.includes(item));
        const isExactMatch = guessValue.length === targetValue.length && 
                            guessValue.every(item => targetValue.includes(item));
        return isExactMatch ? 'correct' : (hasMatch ? 'partial' : 'incorrect');
      }
      return guessValue === targetValue ? 'correct' : 'incorrect';
    } else if (type === 'year') {
      if (guessValue === targetValue) {
        return 'correct';
      } else {
        return 'incorrect';
      }
    }
    return 'incorrect';
  };

  const getYearDisplay = (guessYear: number, targetYear: number): string => {
    if (guessYear === targetYear) {
      return guessYear.toString();
    } else if (guessYear < targetYear) {
      return `${guessYear} ↑`;
    } else {
      return `${guessYear} ↓`;
    }
  };

  const getYearClass = (guessYear: number, targetYear: number): string => {
    if (guessYear === targetYear) {
      return 'correct';
    } else {
      return 'partial'; // Always yellow for directional hints
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 text-gray-100 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-r from-orange-500/10 via-transparent to-orange-500/10 animate-pulse"></div>
      <div className="absolute top-10 left-10 w-16 h-32 bg-gradient-to-b from-orange-400/80 to-red-500/40 rounded-full blur-sm animate-pulse"></div>
      <div className="absolute top-10 right-10 w-16 h-32 bg-gradient-to-b from-orange-400/80 to-red-500/40 rounded-full blur-sm animate-pulse"></div>
      
      <div className="relative z-10 p-4 md:p-8">
        {/* Header */}
        <header className="text-center mb-8">
          <h1 className="text-4xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text text-transparent animate-pulse">
            Champion Dungeon
          </h1>
          <p className="text-xl text-yellow-300">Guess the League of Legends Champion</p>
        </header>

        {/* Game Area */}
        <main className="max-w-7xl mx-auto">
          {/* Input Section */}
          <div className="mb-8 relative">
            <div className="flex gap-4 max-w-2xl mx-auto">
              <div className="flex-1 relative">
                <input
                  ref={inputRef}
                  type="text"
                  value={championInput}
                  onChange={(e) => setChampionInput(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Enter champion name..."
                  className="w-full p-4 text-lg bg-gray-800/80 border-2 border-yellow-600/40 rounded-lg text-gray-100 placeholder-gray-400 focus:outline-none focus:border-yellow-400 focus:ring-2 focus:ring-yellow-400/20 backdrop-blur-sm"
                />
                
                {/* Autocomplete Dropdown */}
                {showAutocomplete && (
                  <div className="absolute top-full left-0 right-0 bg-gray-800/95 border-2 border-yellow-600/40 border-t-0 rounded-b-lg max-h-64 overflow-y-auto z-50 backdrop-blur-sm">
                    {autocompleteMatches.map((champion, index) => (
                      <div
                        key={champion.name}
                        className={`p-3 cursor-pointer flex items-center gap-3 border-b border-yellow-600/20 transition-colors ${
                          index === selectedIndex ? 'bg-yellow-600/20' : 'hover:bg-yellow-600/10'
                        }`}
                        onClick={() => selectChampion(champion.name)}
                      >
                        <Image
                          src={champion.icon}
                          alt={champion.name}
                          width={32}
                          height={32}
                          className="rounded border border-yellow-600/30"
                        />
                        <span>{champion.name}</span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              <button
                onClick={submitGuess}
                className="px-8 py-4 bg-gradient-to-r from-yellow-600 to-orange-600 text-gray-900 font-bold rounded-lg hover:from-yellow-500 hover:to-orange-500 transition-all duration-200 transform hover:scale-105 shadow-lg"
              >
                Guess
              </button>
            </div>
          </div>

          {/* Game Status */}
          <div className="text-center mb-8">
            <p className="text-lg bg-gray-800/60 p-4 rounded-lg border border-yellow-600/30 backdrop-blur-sm">
              {gameStatus}
            </p>
          </div>

          {/* Guesses Table */}
          <div className="bg-gray-800/80 rounded-lg border border-yellow-600/30 overflow-hidden backdrop-blur-sm mb-8">
            {/* Table Header */}
            <div className="grid grid-cols-8 bg-gradient-to-r from-yellow-600/30 to-orange-600/30 border-b border-yellow-600/40">
              <div className="p-3 font-bold text-center text-sm">Champion</div>
              <div className="p-3 font-bold text-center text-sm">Gender</div>
              <div className="p-3 font-bold text-center text-sm">Position</div>
              <div className="p-3 font-bold text-center text-sm">Species</div>
              <div className="p-3 font-bold text-center text-sm">Resource</div>
              <div className="p-3 font-bold text-center text-sm">Range</div>
              <div className="p-3 font-bold text-center text-sm">Region</div>
              <div className="p-3 font-bold text-center text-sm">Year</div>
            </div>
            
            {/* Guesses */}
            <div className="min-h-[100px]">
              {guesses.slice().reverse().map((guess, index) => (
                <div key={`${guess.name}-${index}`} className="grid grid-cols-8 border-b border-yellow-600/20 animate-fadeIn">
                  {/* Champion */}
                  <div className="p-3 flex items-center justify-center gap-2">
                    <Image
                      src={guess.icon}
                      alt={guess.name}
                      width={32}
                      height={32}
                      className="rounded border border-yellow-600/30"
                    />
                    <span className="text-sm font-medium">{guess.name}</span>
                  </div>
                  
                  {/* Gender */}
                  <div className={`p-3 text-center text-sm ${getComparisonClass(guess.gender, currentChampion?.gender, 'exact')} cell-feedback`}>
                    {guess.gender}
                  </div>
                  
                  {/* Position */}
                  <div className={`p-3 text-center text-sm ${getComparisonClass(guess.positions, currentChampion?.positions, 'array')} cell-feedback`}>
                    {guess.positions.join(', ')}
                  </div>
                  
                  {/* Species */}
                  <div className={`p-3 text-center text-sm ${getComparisonClass(guess.species, currentChampion?.species, 'exact')} cell-feedback`}>
                    {guess.species}
                  </div>
                  
                  {/* Resource */}
                  <div className={`p-3 text-center text-sm ${getComparisonClass(guess.resource, currentChampion?.resource, 'exact')} cell-feedback`}>
                    {guess.resource}
                  </div>
                  
                  {/* Range */}
                  <div className={`p-3 text-center text-sm ${getComparisonClass(guess.range_type, currentChampion?.range_type, 'exact')} cell-feedback`}>
                    {guess.range_type}
                  </div>
                  
                  {/* Region */}
                  <div className={`p-3 text-center text-sm ${getComparisonClass(guess.regions, currentChampion?.regions, 'array')} cell-feedback`}>
                    {guess.regions.join(', ')}
                  </div>
                  
                  {/* Year with directional hints */}
                  <div className={`p-3 text-center text-sm ${getYearClass(guess.release_year, currentChampion?.release_year || 0)} cell-feedback`}>
                    {getYearDisplay(guess.release_year, currentChampion?.release_year || 0)}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Game Controls */}
          <div className="flex justify-center gap-4 mb-8">
            <button
              onClick={startNewGame}
              className="px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-bold rounded-lg hover:from-green-500 hover:to-emerald-500 transition-all duration-200 transform hover:scale-105"
            >
              New Game
            </button>
            <button
              onClick={giveUp}
              disabled={gameOver}
              className="px-6 py-3 bg-gradient-to-r from-red-600 to-pink-600 text-white font-bold rounded-lg hover:from-red-500 hover:to-pink-500 transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Give Up
            </button>
            <button
              onClick={() => setShowRulesModal(true)}
              className="px-6 py-3 bg-gradient-to-r from-blue-600 to-cyan-600 text-white font-bold rounded-lg hover:from-blue-500 hover:to-cyan-500 transition-all duration-200 transform hover:scale-105"
            >
              Rules
            </button>
          </div>
        </main>
      </div>

      {/* Victory Modal */}
      {showVictoryModal && currentChampion && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 backdrop-blur-sm">
          <div className="bg-gradient-to-br from-gray-800 to-gray-900 p-8 rounded-xl border-2 border-yellow-500 max-w-md w-full mx-4 text-center">
            <h2 className="text-3xl font-bold mb-4 text-yellow-400">🏆 Victory! 🏆</h2>
            <div className="mb-6">
              <Image
                src={currentChampion.icon}
                alt={currentChampion.name}
                width={100}
                height={100}
                className="mx-auto rounded-lg border-2 border-yellow-500 mb-4"
              />
              <h3 className="text-2xl font-bold text-yellow-300">{currentChampion.name}</h3>
            </div>
            <p className="text-lg mb-6">You guessed it in {guesses.length} attempt{guesses.length === 1 ? '' : 's'}!</p>
            <div className="flex gap-4 justify-center">
              <button
                onClick={() => {
                  setShowVictoryModal(false);
                  startNewGame();
                }}
                className="px-6 py-3 bg-gradient-to-r from-yellow-600 to-orange-600 text-gray-900 font-bold rounded-lg hover:from-yellow-500 hover:to-orange-500 transition-all duration-200"
              >
                Play Again
              </button>
              <button
                onClick={() => setShowVictoryModal(false)}
                className="px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 text-white font-bold rounded-lg hover:from-gray-500 hover:to-gray-600 transition-all duration-200"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Rules Modal */}
      {showRulesModal && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 backdrop-blur-sm">
          <div className="bg-gradient-to-br from-gray-800 to-gray-900 p-8 rounded-xl border-2 border-yellow-500 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-3xl font-bold text-yellow-400">How to Play</h2>
              <button
                onClick={() => setShowRulesModal(false)}
                className="text-2xl text-gray-400 hover:text-white transition-colors"
              >
                ×
              </button>
            </div>
            
            <div className="space-y-6">
              <div>
                <h3 className="text-xl font-bold text-yellow-300 mb-2">🎯 Objective</h3>
                <p className="text-gray-300">Guess the mystery League of Legends champion using the clues provided after each guess.</p>
              </div>
              
              <div>
                <h3 className="text-xl font-bold text-yellow-300 mb-2">🎨 Color Guide</h3>
                <div className="space-y-2">
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 bg-green-500 rounded"></div>
                    <span>Green = Correct match</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 bg-yellow-500 rounded"></div>
                    <span>Yellow = Partial match (positions/regions) or directional hint (year)</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 bg-red-500 rounded"></div>
                    <span>Red = Incorrect</span>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-xl font-bold text-yellow-300 mb-2">📊 Categories</h3>
                <ul className="space-y-1 text-gray-300">
                  <li><strong>Gender:</strong> Male, Female, or Other</li>
                  <li><strong>Position:</strong> Top, Jungle, Mid, Bot, Support roles</li>
                  <li><strong>Species:</strong> Human, Yordle, Vastaya, etc.</li>
                  <li><strong>Resource:</strong> Mana, Energy, etc.</li>
                  <li><strong>Range:</strong> Melee or Ranged</li>
                  <li><strong>Region:</strong> Demacia, Noxus, Ionia, etc.</li>
                  <li><strong>Year:</strong> Release year with ↑ (too low) or ↓ (too high) hints</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

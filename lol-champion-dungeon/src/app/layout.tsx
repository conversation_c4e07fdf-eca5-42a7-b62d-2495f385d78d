import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON>, Crimson_Text, Uncial_Antiqua } from "next/font/google";
import "./globals.css";

const cinzel = Cinzel({
  variable: "--font-cinzel",
  subsets: ["latin"],
  weight: ["400", "600", "700"],
});

const crimsonText = Crimson_Text({
  variable: "--font-crimson-text",
  subsets: ["latin"],
  weight: ["400", "600", "700"],
});

const uncialAntiqua = Uncial_Antiqua({
  variable: "--font-uncial-antiqua",
  subsets: ["latin"],
  weight: ["400"],
});

export const metadata: Metadata = {
  title: "LoL Champion Dungeon - Guess the Champion",
  description: "A League of Legends guessing game where you identify champions based on their attributes. Dark dungeon theme with atmospheric effects.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${cinzel.variable} ${crimsonText.variable} ${uncialAntiqua.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}

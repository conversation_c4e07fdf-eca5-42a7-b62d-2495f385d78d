@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Crimson+Text:wght@400;600;700&family=Uncial+Antiqua&display=swap');

:root {
  --background: #f4f1e8;
  --foreground: #2c1810;
  --leather-brown: #8b4513;
  --gold-accent: #d4af37;
  --bronze-accent: #cd7f32;
  --parchment: #f7f3e9;
  --stone-gray: #6b6b6b;
  --deep-brown: #3e2723;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Crimson Text', serif;
  --font-mono: 'Cinzel', serif;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Crimson Text', serif;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(139, 69, 19, 0.1) 0%, transparent 50%),
    linear-gradient(45deg, transparent 49%, rgba(212, 175, 55, 0.02) 50%, transparent 51%);
}

/* Skeuomorphic Base Styles */
.leather-texture {
  background:
    linear-gradient(135deg, #8b4513 0%, #a0522d 25%, #8b4513 50%, #654321 75%, #8b4513 100%),
    radial-gradient(circle at 30% 30%, rgba(160, 82, 45, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 70% 70%, rgba(101, 67, 33, 0.3) 0%, transparent 50%);
  background-size: 100% 100%, 20px 20px, 15px 15px;
}

.parchment-texture {
  background:
    linear-gradient(135deg, #f7f3e9 0%, #f4f1e8 25%, #f0ede4 50%, #ede9e0 75%, #f7f3e9 100%),
    radial-gradient(circle at 20% 20%, rgba(212, 175, 55, 0.1) 0%, transparent 30%),
    radial-gradient(circle at 80% 80%, rgba(139, 69, 19, 0.05) 0%, transparent 30%);
  background-size: 100% 100%, 40px 40px, 30px 30px;
}

.metal-texture {
  background:
    linear-gradient(135deg, #c0c0c0 0%, #a8a8a8 25%, #c0c0c0 50%, #909090 75%, #c0c0c0 100%),
    linear-gradient(45deg, transparent 40%, rgba(255, 255, 255, 0.3) 50%, transparent 60%);
  background-size: 100% 100%, 10px 10px;
}

.stone-texture {
  background:
    linear-gradient(135deg, #6b6b6b 0%, #808080 25%, #6b6b6b 50%, #555555 75%, #6b6b6b 100%),
    radial-gradient(circle at 40% 40%, rgba(128, 128, 128, 0.3) 0%, transparent 50%);
  background-size: 100% 100%, 25px 25px;
}

/* Skeuomorphic Button Styles */
.skeu-button {
  position: relative;
  background: linear-gradient(145deg, #d4af37, #b8941f);
  border: 2px solid #8b7355;
  border-radius: 8px;
  box-shadow:
    0 8px 16px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.skeu-button:hover {
  background: linear-gradient(145deg, #e6c547, #c9a429);
  box-shadow:
    0 10px 20px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    inset 0 -1px 0 rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

.skeu-button:active {
  background: linear-gradient(145deg, #b8941f, #d4af37);
  box-shadow:
    0 4px 8px rgba(0, 0, 0, 0.3),
    inset 0 2px 4px rgba(0, 0, 0, 0.2);
  transform: translateY(1px);
}

/* Skeuomorphic Input Styles */
.skeu-input {
  background:
    linear-gradient(145deg, #f7f3e9, #f0ede4),
    radial-gradient(circle at 30% 30%, rgba(212, 175, 55, 0.1) 0%, transparent 50%);
  border: 3px solid #8b7355;
  border-radius: 6px;
  box-shadow:
    inset 0 3px 6px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.skeu-input:focus {
  border-color: #d4af37;
  box-shadow:
    inset 0 3px 6px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    0 0 0 3px rgba(212, 175, 55, 0.3),
    0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Skeuomorphic Card Styles */
.skeu-card {
  background:
    linear-gradient(145deg, #f7f3e9, #ede9e0),
    radial-gradient(circle at 20% 20%, rgba(212, 175, 55, 0.08) 0%, transparent 40%);
  border: 2px solid #c4a574;
  border-radius: 12px;
  box-shadow:
    0 12px 24px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

/* Skeuomorphic Table Styles */
.skeu-table {
  background:
    linear-gradient(145deg, #f4f1e8, #e8e5dc),
    radial-gradient(circle at 30% 30%, rgba(139, 69, 19, 0.05) 0%, transparent 50%);
  border: 3px solid #8b7355;
  border-radius: 10px;
  box-shadow:
    0 8px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.skeu-table-header {
  background:
    linear-gradient(145deg, #8b4513, #654321),
    linear-gradient(90deg, transparent 40%, rgba(212, 175, 55, 0.2) 50%, transparent 60%);
  border-bottom: 2px solid #5d2f0a;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Feedback Cell Styles - Skeuomorphic */
.cell-feedback.correct {
  background:
    linear-gradient(145deg, #4ade80, #22c55e),
    radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3) 0%, transparent 50%);
  border: 1px solid #16a34a;
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2),
    0 2px 4px rgba(0, 0, 0, 0.1);
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.cell-feedback.partial {
  background:
    linear-gradient(145deg, #fbbf24, #f59e0b),
    radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3) 0%, transparent 50%);
  border: 1px solid #d97706;
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2),
    0 2px 4px rgba(0, 0, 0, 0.1);
  color: #92400e;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
  font-weight: 600;
}

.cell-feedback.incorrect {
  background:
    linear-gradient(145deg, #ef4444, #dc2626),
    radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3) 0%, transparent 50%);
  border: 1px solid #b91c1c;
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2),
    0 2px 4px rgba(0, 0, 0, 0.1);
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Modal Styles - Skeuomorphic */
.skeu-modal {
  background:
    linear-gradient(145deg, #f7f3e9, #ede9e0),
    radial-gradient(circle at 25% 25%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(139, 69, 19, 0.05) 0%, transparent 50%);
  border: 4px solid #8b7355;
  border-radius: 16px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.4),
    inset 0 -2px 0 rgba(0, 0, 0, 0.1);
}

/* Autocomplete Dropdown - Skeuomorphic */
.skeu-dropdown {
  background:
    linear-gradient(145deg, #f7f3e9, #f0ede4),
    radial-gradient(circle at 30% 30%, rgba(212, 175, 55, 0.08) 0%, transparent 50%);
  border: 2px solid #8b7355;
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow:
    0 8px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.skeu-dropdown-item {
  border-bottom: 1px solid rgba(139, 115, 85, 0.2);
  transition: all 0.2s ease;
}

.skeu-dropdown-item:hover,
.skeu-dropdown-item.selected {
  background:
    linear-gradient(145deg, #d4af37, #c9a429),
    radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.3);
  color: #2c1810;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
}

/* Typography Enhancements */
.skeu-title {
  font-family: 'Uncial Antiqua', cursive;
  background: linear-gradient(145deg, #d4af37, #b8941f, #8b7355);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* Animation Enhancements */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.shimmer-effect {
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .skeu-button {
    box-shadow:
      0 4px 8px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.3),
      inset 0 -1px 0 rgba(0, 0, 0, 0.2);
  }

  .skeu-card {
    box-shadow:
      0 6px 12px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.4),
      inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  }
}

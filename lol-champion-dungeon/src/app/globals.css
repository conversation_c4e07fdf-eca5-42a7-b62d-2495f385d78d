@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Creepster&display=swap');

:root {
  --background: #0a0a0a;
  --foreground: #ededed;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Cinzel', serif;
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Cinzel', serif;
}

/* Custom styles for the champion dungeon game */
.cell-feedback.correct {
  @apply bg-green-600/80 text-white;
  box-shadow: inset 0 0 10px rgba(34, 139, 34, 0.3);
}

.cell-feedback.partial {
  @apply bg-yellow-500/80 text-gray-900;
  box-shadow: inset 0 0 10px rgba(255, 165, 0, 0.3);
}

.cell-feedback.incorrect {
  @apply bg-red-600/80 text-white;
  box-shadow: inset 0 0 10px rgba(220, 20, 60, 0.3);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}

#!/usr/bin/env python3
"""
League of Legends Champions Database Builder

This script fetches champion data from Riot Data Dragon API and merges it with
additional information from a CSV file to create a comprehensive champions database.
"""

import json
import csv
import requests
from typing import Dict, List, Any
import sys


def fetch_latest_version() -> str:
    """Fetch the latest version from Riot Data Dragon API."""
    try:
        response = requests.get("https://ddragon.leagueoflegends.com/api/versions.json")
        response.raise_for_status()
        versions = response.json()
        return versions[0]  # First version is the latest
    except requests.RequestException as e:
        print(f"Error fetching version: {e}")
        sys.exit(1)


def fetch_champion_data(version: str) -> Dict[str, Any]:
    """Fetch champion data from Riot Data Dragon API."""
    url = f"https://ddragon.leagueoflegends.com/cdn/{version}/data/en_US/champion.json"
    try:
        response = requests.get(url)
        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        print(f"Error fetching champion data: {e}")
        sys.exit(1)


def load_additional_info(csv_file: str) -> Dict[str, Dict[str, Any]]:
    """Load additional champion information from CSV file."""
    additional_info = {}
    try:
        with open(csv_file, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                name = row['name'].strip()
                additional_info[name] = {
                    'gender': row['gender'].strip(),
                    'species': row['species'].strip(),
                    'regions': [region.strip() for region in row['regions'].split(';') if region.strip()],
                    'release_year': int(row['release_year']) if row['release_year'].strip() else None
                }
    except FileNotFoundError:
        print(f"CSV file '{csv_file}' not found. Please create it with the required champion information.")
        sys.exit(1)
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        sys.exit(1)
    
    return additional_info


def determine_range_type(tags: List[str]) -> str:
    """Determine if champion is Ranged or Melee based on tags."""
    ranged_tags = {'Mage', 'Marksman'}
    return 'Ranged' if any(tag in ranged_tags for tag in tags) else 'Melee'


def process_champion_data(champion_data: Dict[str, Any], additional_info: Dict[str, Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Process and merge champion data from API and CSV."""
    champions = []
    
    for champion_id, champion_info in champion_data['data'].items():
        name = champion_info['name']
        
        # Get additional info for this champion
        extra_info = additional_info.get(name, {})
        
        # Process the champion data
        processed_champion = {
            'name': name,
            'gender': extra_info.get('gender', 'Unknown'),
            'positions': champion_info.get('tags', []),
            'species': extra_info.get('species', 'Unknown'),
            'resource': champion_info.get('partype', 'Unknown'),
            'range_type': determine_range_type(champion_info.get('tags', [])),
            'regions': extra_info.get('regions', []),
            'release_year': extra_info.get('release_year', None)
        }
        
        champions.append(processed_champion)
    
    # Sort champions by name for consistency
    champions.sort(key=lambda x: x['name'])
    return champions


def save_champions_json(champions: List[Dict[str, Any]], output_file: str):
    """Save the processed champions data to JSON file."""
    try:
        with open(output_file, 'w', encoding='utf-8') as file:
            json.dump(champions, file, indent=2, ensure_ascii=False)
        print(f"Successfully saved {len(champions)} champions to {output_file}")
    except Exception as e:
        print(f"Error saving JSON file: {e}")
        sys.exit(1)


def main():
    """Main function to orchestrate the data collection and processing."""
    print("League of Legends Champions Database Builder")
    print("=" * 50)
    
    # Step 1: Fetch latest version
    print("Fetching latest version from Riot API...")
    version = fetch_latest_version()
    print(f"Latest version: {version}")
    
    # Step 2: Fetch champion data
    print("Fetching champion data from Data Dragon...")
    champion_data = fetch_champion_data(version)
    print(f"Found {len(champion_data['data'])} champions in API data")
    
    # Step 3: Load additional information from CSV
    print("Loading additional champion information from CSV...")
    csv_file = "champions_info.csv"
    additional_info = load_additional_info(csv_file)
    print(f"Loaded additional info for {len(additional_info)} champions")
    
    # Step 4: Process and merge data
    print("Processing and merging champion data...")
    champions = process_champion_data(champion_data, additional_info)
    
    # Step 5: Save to JSON file
    output_file = "champions.json"
    save_champions_json(champions, output_file)
    
    # Summary
    print("\nSummary:")
    print(f"- API Champions: {len(champion_data['data'])}")
    print(f"- CSV Champions: {len(additional_info)}")
    print(f"- Final Champions: {len(champions)}")
    
    # Check for missing data
    missing_info = []
    for champion in champions:
        if champion['gender'] == 'Unknown' or champion['species'] == 'Unknown' or not champion['regions']:
            missing_info.append(champion['name'])
    
    if missing_info:
        print(f"\nWarning: {len(missing_info)} champions have missing additional information:")
        for name in missing_info[:10]:  # Show first 10
            print(f"  - {name}")
        if len(missing_info) > 10:
            print(f"  ... and {len(missing_info) - 10} more")
        print("Please update the CSV file with complete information for all champions.")


if __name__ == "__main__":
    main()

// Game State
let champions = [];
let currentChampion = null;
let guesses = [];
let gameOver = false;
let gameStats = {
    attempts: 0,
    wins: 0,
    totalGames: 0
};

// DOM Elements
const championInput = document.getElementById('championInput');
const submitBtn = document.getElementById('submitGuess');
const autocompleteDiv = document.getElementById('autocomplete');
const guessesTable = document.getElementById('guessesTable');
const gameStatus = document.getElementById('gameStatus');
const newGameBtn = document.getElementById('newGameBtn');
const giveUpBtn = document.getElementById('giveUpBtn');
const rulesBtn = document.getElementById('rulesBtn');

// Modal elements
const victoryModal = document.getElementById('victoryModal');
const rulesModal = document.getElementById('rulesModal');
const playAgainBtn = document.getElementById('playAgainBtn');
const closeVictoryBtn = document.getElementById('closeVictoryBtn');
const closeRulesBtn = document.getElementById('closeRulesBtn');

// Stats elements
const attemptsEl = document.getElementById('attempts');
const winsEl = document.getElementById('wins');
const winRateEl = document.getElementById('winRate');

// Initialize Game
document.addEventListener('DOMContentLoaded', async () => {
    await loadChampions();
    loadGameStats();
    updateStatsDisplay();
    startNewGame();
    setupEventListeners();
});

// Load Champions Data
async function loadChampions() {
    try {
        const response = await fetch('champions.json');
        champions = await response.json();
        console.log(`Loaded ${champions.length} champions`);
    } catch (error) {
        console.error('Error loading champions:', error);
        gameStatus.innerHTML = '<p style="color: #ff6347;">Error loading champion data. Please refresh the page.</p>';
    }
}

// Setup Event Listeners
function setupEventListeners() {
    championInput.addEventListener('input', handleInput);
    championInput.addEventListener('keydown', handleKeyDown);
    submitBtn.addEventListener('click', submitGuess);
    newGameBtn.addEventListener('click', startNewGame);
    giveUpBtn.addEventListener('click', giveUp);
    rulesBtn.addEventListener('click', () => showModal(rulesModal));
    
    // Modal event listeners
    playAgainBtn.addEventListener('click', () => {
        hideModal(victoryModal);
        startNewGame();
    });
    closeVictoryBtn.addEventListener('click', () => hideModal(victoryModal));
    closeRulesBtn.addEventListener('click', () => hideModal(rulesModal));
    
    // Close modals when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target === victoryModal) hideModal(victoryModal);
        if (e.target === rulesModal) hideModal(rulesModal);
    });
    
    // Hide autocomplete when clicking outside
    document.addEventListener('click', (e) => {
        if (!championInput.contains(e.target) && !autocompleteDiv.contains(e.target)) {
            hideAutocomplete();
        }
    });
}

// Handle Input for Autocomplete
function handleInput(e) {
    const query = e.target.value.toLowerCase().trim();
    
    if (query.length === 0) {
        hideAutocomplete();
        return;
    }
    
    const matches = champions.filter(champion => 
        champion.name.toLowerCase().includes(query) &&
        !guesses.some(guess => guess.name === champion.name)
    ).slice(0, 8);
    
    showAutocomplete(matches);
}

// Handle Keyboard Navigation
function handleKeyDown(e) {
    const items = autocompleteDiv.querySelectorAll('.autocomplete-item');
    const selected = autocompleteDiv.querySelector('.autocomplete-item.selected');
    
    if (e.key === 'ArrowDown') {
        e.preventDefault();
        if (!selected) {
            items[0]?.classList.add('selected');
        } else {
            selected.classList.remove('selected');
            const next = selected.nextElementSibling || items[0];
            next.classList.add('selected');
        }
    } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        if (!selected) {
            items[items.length - 1]?.classList.add('selected');
        } else {
            selected.classList.remove('selected');
            const prev = selected.previousElementSibling || items[items.length - 1];
            prev.classList.add('selected');
        }
    } else if (e.key === 'Enter') {
        e.preventDefault();
        if (selected) {
            selectChampion(selected.dataset.championName);
        } else {
            submitGuess();
        }
    } else if (e.key === 'Escape') {
        hideAutocomplete();
    }
}

// Show Autocomplete
function showAutocomplete(matches) {
    if (matches.length === 0) {
        hideAutocomplete();
        return;
    }
    
    autocompleteDiv.innerHTML = matches.map(champion => `
        <div class="autocomplete-item" data-champion-name="${champion.name}">
            <img src="${champion.icon}" alt="${champion.name}" onerror="this.style.display='none'">
            <span>${champion.name}</span>
        </div>
    `).join('');
    
    // Add click listeners
    autocompleteDiv.querySelectorAll('.autocomplete-item').forEach(item => {
        item.addEventListener('click', () => selectChampion(item.dataset.championName));
    });
    
    autocompleteDiv.style.display = 'block';
}

// Hide Autocomplete
function hideAutocomplete() {
    autocompleteDiv.style.display = 'none';
}

// Select Champion from Autocomplete
function selectChampion(championName) {
    championInput.value = championName;
    hideAutocomplete();
    submitGuess();
}

// Submit Guess
function submitGuess() {
    if (gameOver) return;
    
    const guessName = championInput.value.trim();
    if (!guessName) return;
    
    const champion = champions.find(c => c.name.toLowerCase() === guessName.toLowerCase());
    if (!champion) {
        showGameStatus('Champion not found. Please select from the dropdown.', 'error');
        return;
    }
    
    if (guesses.some(g => g.name === champion.name)) {
        showGameStatus('You already guessed this champion!', 'error');
        return;
    }
    
    // Add guess
    guesses.push(champion);
    championInput.value = '';
    hideAutocomplete();
    
    // Update display
    addGuessToTable(champion);
    
    // Check if correct
    if (champion.name === currentChampion.name) {
        winGame();
    } else {
        showGameStatus(`Not ${champion.name}. Keep trying!`, 'info');
    }
}

// Add Guess to Table
function addGuessToTable(guess) {
    const row = document.createElement('div');
    row.className = 'guess-row';
    
    const cells = [
        createChampionCell(guess),
        createComparisonCell(guess.gender, currentChampion.gender, 'exact'),
        createComparisonCell(guess.positions, currentChampion.positions, 'array'),
        createComparisonCell(guess.species, currentChampion.species, 'exact'),
        createComparisonCell(guess.resource, currentChampion.resource, 'exact'),
        createComparisonCell(guess.range_type, currentChampion.range_type, 'exact'),
        createComparisonCell(guess.regions, currentChampion.regions, 'array'),
        createComparisonCell(guess.release_year, currentChampion.release_year, 'year')
    ];
    
    cells.forEach(cell => row.appendChild(cell));
    guessesTable.insertBefore(row, guessesTable.firstChild);
}

// Create Champion Cell
function createChampionCell(champion) {
    const cell = document.createElement('div');
    cell.className = 'guess-cell champion-cell';
    cell.innerHTML = `
        <img src="${champion.icon}" alt="${champion.name}" onerror="this.style.display='none'">
        <span class="champion-name">${champion.name}</span>
    `;
    return cell;
}

// Create Comparison Cell
function createComparisonCell(guessValue, targetValue, type) {
    const cell = document.createElement('div');
    cell.className = 'guess-cell';
    
    let displayValue = '';
    let feedbackClass = '';
    
    if (type === 'exact') {
        displayValue = guessValue;
        feedbackClass = guessValue === targetValue ? 'correct' : 'incorrect';
    } else if (type === 'array') {
        displayValue = Array.isArray(guessValue) ? guessValue.join(', ') : guessValue;
        if (Array.isArray(guessValue) && Array.isArray(targetValue)) {
            const hasMatch = guessValue.some(item => targetValue.includes(item));
            const isExactMatch = guessValue.length === targetValue.length && 
                                guessValue.every(item => targetValue.includes(item));
            feedbackClass = isExactMatch ? 'correct' : (hasMatch ? 'partial' : 'incorrect');
        } else {
            feedbackClass = guessValue === targetValue ? 'correct' : 'incorrect';
        }
    } else if (type === 'year') {
        displayValue = guessValue;
        if (guessValue === targetValue) {
            feedbackClass = 'correct';
        } else {
            const diff = Math.abs(guessValue - targetValue);
            feedbackClass = diff <= 2 ? 'partial' : 'incorrect';
        }
    }
    
    cell.textContent = displayValue;
    cell.classList.add(feedbackClass);
    
    return cell;
}

// Start New Game
function startNewGame() {
    if (champions.length === 0) return;
    
    currentChampion = champions[Math.floor(Math.random() * champions.length)];
    guesses = [];
    gameOver = false;
    
    // Clear UI
    guessesTable.innerHTML = '';
    championInput.value = '';
    hideAutocomplete();
    
    showGameStatus('A new champion awaits! Start guessing...', 'info');
    championInput.focus();
    
    console.log('New game started. Target champion:', currentChampion.name);
}

// Win Game
function winGame() {
    gameOver = true;
    gameStats.wins++;
    gameStats.totalGames++;
    saveGameStats();
    updateStatsDisplay();
    
    showGameStatus(`🎉 Congratulations! You found ${currentChampion.name}!`, 'success');
    
    // Show victory modal
    document.getElementById('victoryChampionIcon').src = currentChampion.icon;
    document.getElementById('victoryChampionName').textContent = currentChampion.name;
    document.getElementById('victoryMessage').textContent = `You guessed it in ${guesses.length} attempt${guesses.length === 1 ? '' : 's'}!`;
    document.getElementById('victoryAttempts').textContent = guesses.length;
    
    setTimeout(() => showModal(victoryModal), 1000);
}

// Give Up
function giveUp() {
    if (gameOver) return;
    
    gameOver = true;
    gameStats.totalGames++;
    saveGameStats();
    updateStatsDisplay();
    
    showGameStatus(`The champion was ${currentChampion.name}. Better luck next time!`, 'error');
    
    // Add the correct answer to the table
    addGuessToTable(currentChampion);
}

// Show Game Status
function showGameStatus(message, type) {
    gameStatus.innerHTML = `<p class="${type}">${message}</p>`;
    
    // Add some styling based on type
    const statusEl = gameStatus.querySelector('p');
    if (type === 'error') {
        statusEl.style.color = '#ff6347';
    } else if (type === 'success') {
        statusEl.style.color = '#32cd32';
    } else if (type === 'info') {
        statusEl.style.color = '#87ceeb';
    }
}

// Modal Functions
function showModal(modal) {
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function hideModal(modal) {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Game Stats Functions
function loadGameStats() {
    const saved = localStorage.getItem('lolChampionDungeonStats');
    if (saved) {
        gameStats = { ...gameStats, ...JSON.parse(saved) };
    }
}

function saveGameStats() {
    localStorage.setItem('lolChampionDungeonStats', JSON.stringify(gameStats));
}

function updateStatsDisplay() {
    attemptsEl.textContent = gameStats.totalGames;
    winsEl.textContent = gameStats.wins;
    const winRate = gameStats.totalGames > 0 ? Math.round((gameStats.wins / gameStats.totalGames) * 100) : 0;
    winRateEl.textContent = `${winRate}%`;
}
